'use client';
import { useState } from 'react';
import {
    <PERSON><PERSON>,
    Card,
    CardBody,
    CardHeader,
    Col,
    Input,
    InputGroup,
    Row,
    Form,
    FormGroup,
    Label,
    Textarea,
} from 'reactstrap';

const Notes = () => {
    const [notes, setNotes] = useState([
        {
            id: 1,
            title: '<PERSON><PERSON> chú về yêu cầu khách hàng',
            content: 'Kh<PERSON>ch hàng yêu cầu gửi sớm hơn 1 ngày',
            author: '<PERSON><PERSON><PERSON><PERSON>',
            createdAt: '15/02/2025 - 05:47',
            updatedAt: '15/02/2025 - 05:47'
        },
        {
            id: 2,
            title: 'Thông tin sản phẩm',
            content: '<PERSON><PERSON>n cập nhật thông tin chi tiết về sản phẩm mới cho báo giá tháng 2',
            author: 'NVBH Trung Hiếu',
            createdAt: '14/02/2025 - 14:30',
            updatedAt: '14/02/2025 - 14:30'
        }
    ]);
    const [showAddForm, setShowAddForm] = useState(false);
    const [newNote, setNewNote] = useState({ title: '', content: '' });
    const [searchTerm, setSearchTerm] = useState('');

    const handleAddNote = () => {
        if (newNote.title.trim() && newNote.content.trim()) {
            const note = {
                id: Date.now(),
                title: newNote.title,
                content: newNote.content,
                author: 'Người dùng hiện tại',
                createdAt: new Date().toLocaleString('vi-VN'),
                updatedAt: new Date().toLocaleString('vi-VN')
            };
            setNotes([note, ...notes]);
            setNewNote({ title: '', content: '' });
            setShowAddForm(false);
        }
    };

    const filteredNotes = notes.filter(note =>
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.content.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <Col lg={12}>
            <Card>
                <CardHeader>
                    <div className='d-flex justify-content-between align-items-center'>
                        <h5 className='card-title mb-0'>Ghi chú</h5>
                        <Button
                            color='primary'
                            size='sm'
                            onClick={() => setShowAddForm(!showAddForm)}
                        >
                            <i className='ri-add-line align-bottom me-1'></i>
                            Tạo ghi chú mới
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {/* Search bar */}
                    <div className='mb-4'>
                        <InputGroup>
                            <Input
                                placeholder='Tìm kiếm ghi chú...'
                                className='py-2'
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            <Button color='light'>
                                <i className='ri-search-line'></i>
                            </Button>
                        </InputGroup>
                    </div>

                    {/* Add note form */}
                    {showAddForm && (
                        <Card className='mb-4'>
                            <CardBody>
                                <Form>
                                    <FormGroup>
                                        <Label for='noteTitle'>Tiêu đề ghi chú</Label>
                                        <Input
                                            type='text'
                                            id='noteTitle'
                                            placeholder='Nhập tiêu đề ghi chú...'
                                            value={newNote.title}
                                            onChange={(e) => setNewNote({...newNote, title: e.target.value})}
                                        />
                                    </FormGroup>
                                    <FormGroup>
                                        <Label for='noteContent'>Nội dung</Label>
                                        <Input
                                            type='textarea'
                                            id='noteContent'
                                            rows={4}
                                            placeholder='Nhập nội dung ghi chú...'
                                            value={newNote.content}
                                            onChange={(e) => setNewNote({...newNote, content: e.target.value})}
                                        />
                                    </FormGroup>
                                    <div className='d-flex gap-2'>
                                        <Button color='primary' onClick={handleAddNote}>
                                            <i className='ri-save-line align-bottom me-1'></i>
                                            Lưu ghi chú
                                        </Button>
                                        <Button color='secondary' outline onClick={() => setShowAddForm(false)}>
                                            Hủy
                                        </Button>
                                    </div>
                                </Form>
                            </CardBody>
                        </Card>
                    )}

                    {/* Notes list */}
                    <div className='notes-list'>
                        {filteredNotes.length === 0 ? (
                            <div className='text-center py-5'>
                                <i className='ri-file-text-line fs-48 text-muted mb-3'></i>
                                <h5 className='text-muted'>Chưa có ghi chú nào</h5>
                                <p className='text-muted'>Tạo ghi chú đầu tiên của bạn</p>
                            </div>
                        ) : (
                            filteredNotes.map((note) => (
                                <div key={note.id} className='note-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-start justify-content-between mb-2'>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-1'>{note.title}</h6>
                                            <small className='text-muted'>
                                                Tạo bởi {note.author} • {note.createdAt}
                                                {note.updatedAt !== note.createdAt && (
                                                    <span> • Cập nhật {note.updatedAt}</span>
                                                )}
                                            </small>
                                        </div>
                                        <div className='d-flex gap-1'>
                                            <Button
                                                color='light'
                                                size='sm'
                                                className='btn-icon'
                                                title='Chỉnh sửa'
                                            >
                                                <i className='ri-edit-line'></i>
                                            </Button>
                                            <Button
                                                color='light'
                                                size='sm'
                                                className='btn-icon'
                                                title='Xóa'
                                            >
                                                <i className='ri-delete-bin-line'></i>
                                            </Button>
                                            <Button
                                                color='light'
                                                size='sm'
                                                className='btn-icon'
                                                title='Thêm tùy chọn'
                                            >
                                                <i className='ri-more-2-fill'></i>
                                            </Button>
                                        </div>
                                    </div>
                                    <p className='mb-0'>{note.content}</p>
                                </div>
                            ))
                        )}

                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-mail-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Email: Trả lời: Báo giá sản phẩm
                                                tháng 2 - Người gửi:
                                                <EMAIL>
                                            </h6>
                                            <small className='text-muted'>
                                                Đến: NVBH Trung Hiếu
                                            </small>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Tôi đã nhận được thông tin
                                    </p>
                                </div>

                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-calendar-2-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Cuộc hẹn: Đề xuất sản phẩm -
                                                Người chủ trì: NVBH Hiền Lương
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                </div>

                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-checkbox-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Nhiệm vụ: Gửi Email cho khách
                                                hàng - Giao cho: NVBH Đỗ Tiến
                                                Dũng
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Gửi email thông báo cho khách hàng về
                                        sản phẩm mới...
                                    </p>
                                    <div className='mt-2'>
                                        <div className='d-flex align-items-center'>
                                            <small className='text-muted me-2'>
                                                Trạng thái:{' '}
                                            </small>
                                            <span className='badge bg-warning-subtle text-warning'>
                                                Chưa hoàn thành
                                            </span>
                                        </div>
                                        <div className='d-flex align-items-center mt-1'>
                                            <i className='ri-calendar-event-line text-muted me-1'></i>
                                            <small className='text-muted'>
                                                Ngày hết hạn: 20/01/2025 15:00
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='ghichu'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Tạo ghi chú
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-file-text-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Ghi chú - Nguyễn Văn Sỹ
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Khách hàng yêu cầu gửi sớm hơn 1 ngày
                                    </p>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='email'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Gửi email
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-mail-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Email: Trả lời: Báo giá sản phẩm
                                                tháng 2 - Người gửi:
                                                <EMAIL>
                                            </h6>
                                            <small className='text-muted'>
                                                Đến: NVBH Trung Hiếu
                                            </small>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Tôi đã nhận được thông tin
                                    </p>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='cuochen'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Tạo cuộc hẹn
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-calendar-2-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Cuộc hẹn: Đề xuất sản phẩm -
                                                Người chủ trì: NVBH Hiền Lương
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </TabPane>

                        <TabPane tabId='nhiemvu'>
                            <div className='activity-list'>
                                <div className='d-flex justify-content-end mb-3'>
                                    <Button color='primary' size='sm'>
                                        <i className='ri-add-line align-bottom me-1'></i>{' '}
                                        Tạo nhiệm vụ
                                    </Button>
                                </div>
                                <div className='activity-item border rounded p-3 mb-3'>
                                    <div className='d-flex align-items-center mb-2'>
                                        <div className='activity-icon me-3'>
                                            <i className='ri-checkbox-line fs-20'></i>
                                        </div>
                                        <div className='flex-grow-1'>
                                            <h6 className='mb-0'>
                                                Nhiệm vụ: Gửi Email cho khách
                                                hàng - Giao cho: NVBH Đỗ Tiến
                                                Dũng
                                            </h6>
                                        </div>
                                        <div className='activity-time text-muted'>
                                            15/02/2025 - 05:47
                                        </div>
                                        <Button
                                            color='light'
                                            size='sm'
                                            className='btn-icon ms-2'
                                        >
                                            <i className='ri-more-2-fill'></i>
                                        </Button>
                                    </div>
                                    <p className='mb-0'>
                                        Gửi email thông báo cho khách hàng về
                                        sản phẩm mới...
                                    </p>
                                    <div className='mt-2'>
                                        <div className='d-flex align-items-center'>
                                            <small className='text-muted me-2'>
                                                Trạng thái:{' '}
                                            </small>
                                            <span className='badge bg-warning-subtle text-warning'>
                                                Chưa hoàn thành
                                            </span>
                                        </div>
                                        <div className='d-flex align-items-center mt-1'>
                                            <i className='ri-calendar-event-line text-muted me-1'></i>
                                            <small className='text-muted'>
                                                Ngày hết hạn: 20/01/2025 15:00
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </TabPane>
                    </TabContent>
                </CardBody>
            </Card>
        </Col>
    );
};

export default Evaluate;
